import { SignUpForm } from "@/components/sign-up-form";
import { useTranslations } from 'next-intl';
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function SignUpPage() {
  const t = useTranslations();

  return (
    <div className="container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">{t('auth.createAccount')}</CardTitle>
          <CardDescription>
            {t('auth.signUp')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SignUpForm />
          <div className="mt-4 text-center text-sm">
            {t('auth.alreadyHaveAccount')}{" "}
            <Link href="/auth/login" className="underline">
              {t('auth.signIn')}
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
