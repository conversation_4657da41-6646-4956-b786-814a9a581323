import { useTranslations } from 'next-intl';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { 
  BarChart3, 
  CreditCard, 
  PieChart, 
  TrendingUp, 
  Wallet, 
  Shield,
  Smartphone,
  Globe,
  Zap
} from "lucide-react";

export default function HomePage() {
  const t = useTranslations();

  const features = [
    {
      icon: CreditCard,
      title: "智能记账",
      description: "快速记录每一笔收支，支持多种交易类型和分类管理"
    },
    {
      icon: BarChart3,
      title: "数据分析",
      description: "详细的财务报表和趋势分析，让您的财务状况一目了然"
    },
    {
      icon: PieChart,
      title: "预算管理",
      description: "设置预算目标，实时监控支出，避免超支"
    },
    {
      icon: Wallet,
      title: "多账户管理",
      description: "支持银行卡、现金、信用卡等多种账户类型"
    },
    {
      icon: Smartphone,
      title: "移动优先",
      description: "专为移动设备优化，随时随地记录和查看财务数据"
    },
    {
      icon: Globe,
      title: "多语言支持",
      description: "支持中文和英文，满足不同用户的语言需求"
    }
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="container py-12 md:py-24 lg:py-32">
        <div className="flex flex-col items-center text-center space-y-8">
          <div className="space-y-4">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
              {t('app.title')}
            </h1>
            <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
              {t('app.description')}
            </p>
          </div>
          
          <div className="flex flex-col gap-4 sm:flex-row">
            <Button asChild size="lg" className="text-lg px-8">
              <Link href="/auth/sign-up">
                开始使用
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="text-lg px-8">
              <Link href="/dashboard">
                查看演示
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container py-12 md:py-24">
        <div className="text-center space-y-4 mb-12">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">
            核心功能
          </h2>
          <p className="mx-auto max-w-[600px] text-gray-500 md:text-lg dark:text-gray-400">
            专业的记账软件功能，让财务管理变得简单高效
          </p>
        </div>
        
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} className="relative overflow-hidden">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
                      <Icon className="h-5 w-5 text-primary" />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </section>

      {/* Stats Section */}
      <section className="container py-12 md:py-24 bg-muted/50">
        <div className="text-center space-y-4 mb-12">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">
            为什么选择生财记
          </h2>
        </div>
        
        <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
          <div className="text-center space-y-2">
            <div className="text-3xl font-bold text-primary">100%</div>
            <div className="text-sm text-muted-foreground">免费使用</div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-3xl font-bold text-primary">
              <Shield className="h-8 w-8 mx-auto" />
            </div>
            <div className="text-sm text-muted-foreground">数据安全</div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-3xl font-bold text-primary">
              <Zap className="h-8 w-8 mx-auto" />
            </div>
            <div className="text-sm text-muted-foreground">快速响应</div>
          </div>
          <div className="text-center space-y-2">
            <div className="text-3xl font-bold text-primary">
              <TrendingUp className="h-8 w-8 mx-auto" />
            </div>
            <div className="text-sm text-muted-foreground">持续更新</div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-12 md:py-24">
        <div className="flex flex-col items-center text-center space-y-8">
          <div className="space-y-4">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">
              开始您的财务管理之旅
            </h2>
            <p className="mx-auto max-w-[600px] text-gray-500 md:text-lg dark:text-gray-400">
              立即注册，体验专业的记账和财务管理功能
            </p>
          </div>
          
          <Button asChild size="lg" className="text-lg px-8">
            <Link href="/auth/sign-up">
              免费注册
            </Link>
          </Button>
        </div>
      </section>
    </div>
  );
}
