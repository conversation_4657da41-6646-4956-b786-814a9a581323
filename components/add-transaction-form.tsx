"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from 'next-intl';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { toast } from "sonner";
import { createClient } from "@/lib/supabase/client";
import { TransactionService } from "@/lib/services/transactions";
import { AccountService } from "@/lib/services/accounts";
import { CategoryService } from "@/lib/services/categories";
import { Account, Category } from "@/lib/types/database";

export function AddTransactionForm() {
  const t = useTranslations();
  const router = useRouter();
  const supabase = createClient();
  const transactionService = new TransactionService();
  const accountService = new AccountService();
  const categoryService = new CategoryService();

  const [isLoading, setIsLoading] = useState(false);
  const [transactionType, setTransactionType] = useState<'income' | 'expense' | 'transfer'>('expense');
  const [date, setDate] = useState<Date>(new Date());
  const [showCalendar, setShowCalendar] = useState(false);
  const [user, setUser] = useState<any>(null);

  // Data from database
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingData, setLoadingData] = useState(true);

  // Form state
  const [formData, setFormData] = useState({
    amount: '',
    categoryId: '',
    accountId: '',
    fromAccountId: '',
    toAccountId: '',
    description: '',
    tags: ''
  });

  // Load user and data
  useEffect(() => {
    const loadData = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          router.push('/auth/login');
          return;
        }
        setUser(user);

        // Load accounts and categories
        const [accountsData, categoriesData] = await Promise.all([
          accountService.getAccounts(user.id),
          categoryService.getCategories(user.id)
        ]);

        setAccounts(accountsData);
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('加载数据失败');
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsLoading(true);

    try {
      const transactionData = {
        user_id: user.id,
        type: transactionType,
        amount: parseFloat(formData.amount),
        account_id: transactionType === 'transfer' ? formData.fromAccountId : formData.accountId,
        category_id: formData.categoryId || null,
        description: formData.description || null,
        date: format(date, 'yyyy-MM-dd'),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        from_account_id: transactionType === 'transfer' ? formData.fromAccountId : null,
        to_account_id: transactionType === 'transfer' ? formData.toAccountId : null,
      };

      await transactionService.createTransaction(transactionData);
      toast.success('交易记录添加成功！');
      router.push('/transactions');
    } catch (error) {
      toast.error('添加失败，请重试');
      console.error('Error adding transaction:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const filteredCategories = categories.filter(cat => cat.type === transactionType);

  if (loadingData) {
    return <div className="flex justify-center py-8">加载中...</div>;
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Transaction Type */}
      <div className="space-y-2">
        <Label>交易类型</Label>
        <Tabs value={transactionType} onValueChange={(value) => {
          setTransactionType(value as any);
          // Reset form when changing type
          setFormData(prev => ({
            ...prev,
            categoryId: '',
            accountId: '',
            fromAccountId: '',
            toAccountId: ''
          }));
        }}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="expense" className="text-red-600">支出</TabsTrigger>
            <TabsTrigger value="income" className="text-green-600">收入</TabsTrigger>
            <TabsTrigger value="transfer" className="text-blue-600">转账</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Amount */}
      <div className="space-y-2">
        <Label htmlFor="amount">金额 *</Label>
        <div className="relative">
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">¥</span>
          <Input
            id="amount"
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            className="pl-8"
            value={formData.amount}
            onChange={(e) => handleInputChange('amount', e.target.value)}
            required
          />
        </div>
      </div>

      {/* Category */}
      {transactionType !== 'transfer' && (
        <div className="space-y-2">
          <Label htmlFor="category">分类 *</Label>
          <Select value={formData.categoryId} onValueChange={(value) => handleInputChange('categoryId', value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择分类" />
            </SelectTrigger>
            <SelectContent>
              {filteredCategories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    {category.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Account for income/expense */}
      {transactionType !== 'transfer' && (
        <div className="space-y-2">
          <Label htmlFor="account">账户 *</Label>
          <Select value={formData.accountId} onValueChange={(value) => handleInputChange('accountId', value)}>
            <SelectTrigger>
              <SelectValue placeholder="选择账户" />
            </SelectTrigger>
            <SelectContent>
              {accounts.map((account) => (
                <SelectItem key={account.id} value={account.id}>
                  <div className="flex items-center justify-between w-full">
                    <span>{account.name}</span>
                    <span className="text-sm text-muted-foreground">
                      ¥{account.balance.toFixed(2)}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Transfer accounts */}
      {transactionType === 'transfer' && (
        <>
          <div className="space-y-2">
            <Label htmlFor="fromAccount">从账户 *</Label>
            <Select value={formData.fromAccountId} onValueChange={(value) => handleInputChange('fromAccountId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="选择来源账户" />
              </SelectTrigger>
              <SelectContent>
                {accounts.map((account) => (
                  <SelectItem key={account.id} value={account.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{account.name}</span>
                      <span className="text-sm text-muted-foreground">
                        ¥{account.balance.toFixed(2)}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="toAccount">到账户 *</Label>
            <Select value={formData.toAccountId} onValueChange={(value) => handleInputChange('toAccountId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="选择目标账户" />
              </SelectTrigger>
              <SelectContent>
                {accounts.filter(account => account.id !== formData.fromAccountId).map((account) => (
                  <SelectItem key={account.id} value={account.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{account.name}</span>
                      <span className="text-sm text-muted-foreground">
                        ¥{account.balance.toFixed(2)}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </>
      )}

      {/* Date */}
      <div className="space-y-2">
        <Label>日期 *</Label>
        <Popover open={showCalendar} onOpenChange={setShowCalendar}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-start text-left font-normal"
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, "yyyy年MM月dd日", { locale: zhCN }) : "选择日期"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={date}
              onSelect={(date) => {
                setDate(date || new Date());
                setShowCalendar(false);
              }}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">描述</Label>
        <Textarea
          id="description"
          placeholder="添加备注信息..."
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          rows={3}
        />
      </div>

      {/* Tags */}
      <div className="space-y-2">
        <Label htmlFor="tags">标签</Label>
        <Input
          id="tags"
          placeholder="用逗号分隔多个标签，如：工作日,快餐"
          value={formData.tags}
          onChange={(e) => handleInputChange('tags', e.target.value)}
        />
        <p className="text-sm text-muted-foreground">
          标签可以帮助您更好地分类和搜索交易记录
        </p>
      </div>

      {/* Submit Button */}
      <div className="flex gap-4 pt-4">
        <Button
          type="button"
          variant="outline"
          className="flex-1"
          onClick={() => router.back()}
        >
          {t('common.cancel')}
        </Button>
        <Button
          type="submit"
          className="flex-1"
          disabled={
            isLoading ||
            !formData.amount ||
            (transactionType === 'transfer'
              ? !formData.fromAccountId || !formData.toAccountId
              : !formData.accountId || !formData.categoryId
            )
          }
        >
          {isLoading ? t('common.loading') : t('common.save')}
        </Button>
      </div>
    </form>
  );
}
