import { createClient } from "@/lib/supabase/client";
import { Account, InsertAccount, UpdateAccount } from "@/lib/types/database";

export class AccountService {
  private supabase = createClient();

  // 获取用户的所有账户
  async getAccounts(userId: string, includeInactive = false): Promise<Account[]> {
    let query = this.supabase
      .from('accounts')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: true });

    if (!includeInactive) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch accounts: ${error.message}`);
    }

    return data || [];
  }

  // 获取单个账户
  async getAccount(id: string): Promise<Account | null> {
    const { data, error } = await this.supabase
      .from('accounts')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw new Error(`Failed to fetch account: ${error.message}`);
    }

    return data;
  }

  // 创建新账户
  async createAccount(account: InsertAccount): Promise<Account> {
    const { data, error } = await this.supabase
      .from('accounts')
      .insert(account)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create account: ${error.message}`);
    }

    return data;
  }

  // 更新账户
  async updateAccount(id: string, updates: UpdateAccount): Promise<Account> {
    const { data, error } = await this.supabase
      .from('accounts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update account: ${error.message}`);
    }

    return data;
  }

  // 删除账户（软删除，设置为不活跃）
  async deleteAccount(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('accounts')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete account: ${error.message}`);
    }
  }

  // 硬删除账户（谨慎使用）
  async hardDeleteAccount(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('accounts')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to hard delete account: ${error.message}`);
    }
  }

  // 获取账户余额总计
  async getTotalBalance(userId: string, currency = 'CNY'): Promise<number> {
    const { data, error } = await this.supabase
      .from('accounts')
      .select('balance')
      .eq('user_id', userId)
      .eq('currency', currency)
      .eq('is_active', true);

    if (error) {
      throw new Error(`Failed to fetch total balance: ${error.message}`);
    }

    return data?.reduce((total, account) => total + account.balance, 0) || 0;
  }

  // 按类型获取账户余额
  async getBalanceByType(userId: string): Promise<Record<string, number>> {
    const { data, error } = await this.supabase
      .from('accounts')
      .select('type, balance')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) {
      throw new Error(`Failed to fetch balance by type: ${error.message}`);
    }

    const balanceByType: Record<string, number> = {};

    data?.forEach(account => {
      if (balanceByType[account.type]) {
        balanceByType[account.type] += account.balance;
      } else {
        balanceByType[account.type] = account.balance;
      }
    });

    return balanceByType;
  }

  // 验证账户是否属于用户
  async verifyAccountOwnership(accountId: string, userId: string): Promise<boolean> {
    const { data, error } = await this.supabase
      .from('accounts')
      .select('id')
      .eq('id', accountId)
      .eq('user_id', userId)
      .single();

    if (error) {
      return false;
    }

    return !!data;
  }

  // 获取账户类型选项
  getAccountTypeOptions() {
    return [
      { value: 'cash', label: '现金', icon: 'banknote' },
      { value: 'bank_account', label: '银行账户', icon: 'building-2' },
      { value: 'credit_card', label: '信用卡', icon: 'credit-card' },
      { value: 'investment', label: '投资账户', icon: 'trending-up' },
      { value: 'other', label: '其他', icon: 'more-horizontal' }
    ];
  }

  // 获取货币选项
  getCurrencyOptions() {
    return [
      { value: 'CNY', label: '人民币 (¥)', symbol: '¥' },
      { value: 'USD', label: '美元 ($)', symbol: '$' },
      { value: 'EUR', label: '欧元 (€)', symbol: '€' },
      { value: 'JPY', label: '日元 (¥)', symbol: '¥' },
      { value: 'GBP', label: '英镑 (£)', symbol: '£' },
      { value: 'HKD', label: '港币 (HK$)', symbol: 'HK$' }
    ];
  }
}
