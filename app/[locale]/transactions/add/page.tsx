import { useTranslations } from 'next-intl';
import { AddTransactionForm } from "@/components/add-transaction-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function AddTransactionPage() {
  const t = useTranslations();

  return (
    <div className="container py-6 max-w-2xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="sm" asChild>
          <Link href="/transactions">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('transactions.addTransaction')}</h1>
          <p className="text-muted-foreground">
            记录新的收入、支出或转账
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>交易详情</CardTitle>
          <CardDescription>
            请填写交易的详细信息
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AddTransactionForm />
        </CardContent>
      </Card>
    </div>
  );
}
