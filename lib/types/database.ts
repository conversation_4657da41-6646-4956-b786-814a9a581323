export interface Database {
  public: {
    Tables: {
      accounts: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          type: 'cash' | 'bank_account' | 'credit_card' | 'investment' | 'other';
          balance: number;
          currency: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          type: 'cash' | 'bank_account' | 'credit_card' | 'investment' | 'other';
          balance?: number;
          currency?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          type?: 'cash' | 'bank_account' | 'credit_card' | 'investment' | 'other';
          balance?: number;
          currency?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      categories: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          type: 'income' | 'expense';
          color: string;
          icon: string;
          parent_id: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          type: 'income' | 'expense';
          color?: string;
          icon?: string;
          parent_id?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          type?: 'income' | 'expense';
          color?: string;
          icon?: string;
          parent_id?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      transactions: {
        Row: {
          id: string;
          user_id: string;
          account_id: string;
          category_id: string | null;
          type: 'income' | 'expense' | 'transfer';
          amount: number;
          description: string | null;
          date: string;
          tags: string[];
          from_account_id: string | null;
          to_account_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          account_id: string;
          category_id?: string | null;
          type: 'income' | 'expense' | 'transfer';
          amount: number;
          description?: string | null;
          date: string;
          tags?: string[];
          from_account_id?: string | null;
          to_account_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          account_id?: string;
          category_id?: string | null;
          type?: 'income' | 'expense' | 'transfer';
          amount?: number;
          description?: string | null;
          date?: string;
          tags?: string[];
          from_account_id?: string | null;
          to_account_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      budgets: {
        Row: {
          id: string;
          user_id: string;
          category_id: string | null;
          name: string;
          amount: number;
          period: 'monthly' | 'yearly' | 'custom';
          start_date: string;
          end_date: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          category_id?: string | null;
          name: string;
          amount: number;
          period: 'monthly' | 'yearly' | 'custom';
          start_date: string;
          end_date: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          category_id?: string | null;
          name?: string;
          amount?: number;
          period?: 'monthly' | 'yearly' | 'custom';
          start_date?: string;
          end_date?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_settings: {
        Row: {
          id: string;
          user_id: string;
          default_currency: string;
          date_format: string;
          language: string;
          theme: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          default_currency?: string;
          date_format?: string;
          language?: string;
          theme?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          default_currency?: string;
          date_format?: string;
          language?: string;
          theme?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// 便捷类型别名
export type Account = Database['public']['Tables']['accounts']['Row'];
export type Category = Database['public']['Tables']['categories']['Row'];
export type Transaction = Database['public']['Tables']['transactions']['Row'];
export type Budget = Database['public']['Tables']['budgets']['Row'];
export type UserSettings = Database['public']['Tables']['user_settings']['Row'];

export type InsertAccount = Database['public']['Tables']['accounts']['Insert'];
export type InsertCategory = Database['public']['Tables']['categories']['Insert'];
export type InsertTransaction = Database['public']['Tables']['transactions']['Insert'];
export type InsertBudget = Database['public']['Tables']['budgets']['Insert'];
export type InsertUserSettings = Database['public']['Tables']['user_settings']['Insert'];

export type UpdateAccount = Database['public']['Tables']['accounts']['Update'];
export type UpdateCategory = Database['public']['Tables']['categories']['Update'];
export type UpdateTransaction = Database['public']['Tables']['transactions']['Update'];
export type UpdateBudget = Database['public']['Tables']['budgets']['Update'];
export type UpdateUserSettings = Database['public']['Tables']['user_settings']['Update'];

// 扩展类型，包含关联数据
export type TransactionWithDetails = Transaction & {
  account: Account;
  category: Category | null;
  from_account?: Account | null;
  to_account?: Account | null;
};

export type CategoryWithTransactions = Category & {
  transactions: Transaction[];
};

export type AccountWithTransactions = Account & {
  transactions: Transaction[];
};
