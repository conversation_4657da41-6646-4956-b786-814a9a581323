@import "tailwindcss";

/* Tailwind CSS v4 theme configuration */
@theme {
  /* Colors */
  --color-background: #ffffff;
  --color-foreground: #171717;
  --color-card: #ffffff;
  --color-card-foreground: #171717;
  --color-popover: #ffffff;
  --color-popover-foreground: #171717;
  --color-primary: #171717;
  --color-primary-foreground: #fafafa;
  --color-secondary: #f5f5f5;
  --color-secondary-foreground: #171717;
  --color-muted: #f5f5f5;
  --color-muted-foreground: #737373;
  --color-accent: #f5f5f5;
  --color-accent-foreground: #171717;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #fafafa;
  --color-border: #e5e5e5;
  --color-input: #e5e5e5;
  --color-ring: #171717;
  --color-chart-1: #f97316;
  --color-chart-2: #06b6d4;
  --color-chart-3: #3b82f6;
  --color-chart-4: #eab308;
  --color-chart-5: #f59e0b;

  /* Border radius */
  --radius: 0.5rem;

  /* Dark mode colors */
  @media (prefers-color-scheme: dark) {
    --color-background: #0a0a0a;
    --color-foreground: #ededed;
    --color-card: #0a0a0a;
    --color-card-foreground: #ededed;
    --color-popover: #0a0a0a;
    --color-popover-foreground: #ededed;
    --color-primary: #ededed;
    --color-primary-foreground: #171717;
    --color-secondary: #262626;
    --color-secondary-foreground: #ededed;
    --color-muted: #262626;
    --color-muted-foreground: #a3a3a3;
    --color-accent: #262626;
    --color-accent-foreground: #ededed;
    --color-destructive: #7f1d1d;
    --color-destructive-foreground: #ededed;
    --color-border: #262626;
    --color-input: #262626;
    --color-ring: #d4d4d8;
    --color-chart-1: #dc2626;
    --color-chart-2: #0891b2;
    --color-chart-3: #2563eb;
    --color-chart-4: #ca8a04;
    --color-chart-5: #d97706;
  }
}

/* Base styles */
* {
  border-color: var(--color-border);
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: Arial, Helvetica, sans-serif;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations for the professional homepage */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }

  33% {
    transform: translate(30px, -50px) scale(1.1);
  }

  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }

  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Grid pattern background */
.bg-grid-white\/\[0\.02\] {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
}

/* Price comparison animations */
@keyframes slash {
  0% {
    transform: scale(0) rotate(-45deg);
    opacity: 0;
  }

  25% {
    transform: scale(1.3) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 20px rgba(255, 193, 7, 0.8));
  }

  50% {
    transform: scale(1.1) rotate(15deg);
    opacity: 0.9;
  }

  75% {
    transform: scale(1.2) rotate(-10deg);
    opacity: 0.8;
  }

  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.7;
  }
}

@keyframes price-drop {
  0% {
    transform: translateY(-30px) scale(0.5) rotate(-10deg);
    opacity: 0;
    filter: blur(2px);
  }

  30% {
    transform: translateY(-15px) scale(0.8) rotate(5deg);
    opacity: 0.5;
    filter: blur(1px);
  }

  60% {
    transform: translateY(-5px) scale(1.1) rotate(-2deg);
    opacity: 0.8;
    filter: blur(0px);
  }

  80% {
    transform: translateY(2px) scale(1.05) rotate(1deg);
    opacity: 0.9;
  }

  100% {
    transform: translateY(0) scale(1) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 15px rgba(34, 197, 94, 0.6));
  }
}

@keyframes savings-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

.animate-slash {
  animation: slash 3s ease-in-out infinite;
}

.animate-price-drop {
  animation: price-drop 2s ease-out infinite;
  animation-delay: 1s;
}

.animate-savings-pulse {
  animation: savings-pulse 2s ease-in-out infinite;
  animation-delay: 2s;
}