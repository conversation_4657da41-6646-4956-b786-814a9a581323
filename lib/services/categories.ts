import { createClient } from "@/lib/supabase/client";
import { Category, InsertCategory, UpdateCategory } from "@/lib/types/database";

export class CategoryService {
  private supabase = createClient();

  // 获取用户的所有分类
  async getCategories(userId: string, options?: {
    type?: 'income' | 'expense';
    includeInactive?: boolean;
  }): Promise<Category[]> {
    let query = this.supabase
      .from('categories')
      .select('*')
      .eq('user_id', userId)
      .order('name', { ascending: true });

    if (options?.type) {
      query = query.eq('type', options.type);
    }

    if (!options?.includeInactive) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch categories: ${error.message}`);
    }

    return data || [];
  }

  // 获取单个分类
  async getCategory(id: string): Promise<Category | null> {
    const { data, error } = await this.supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw new Error(`Failed to fetch category: ${error.message}`);
    }

    return data;
  }

  // 创建新分类
  async createCategory(category: InsertCategory): Promise<Category> {
    const { data, error } = await this.supabase
      .from('categories')
      .insert(category)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create category: ${error.message}`);
    }

    return data;
  }

  // 更新分类
  async updateCategory(id: string, updates: UpdateCategory): Promise<Category> {
    const { data, error } = await this.supabase
      .from('categories')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update category: ${error.message}`);
    }

    return data;
  }

  // 删除分类（软删除）
  async deleteCategory(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('categories')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete category: ${error.message}`);
    }
  }

  // 硬删除分类（谨慎使用）
  async hardDeleteCategory(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to hard delete category: ${error.message}`);
    }
  }

  // 获取分类树结构（包含子分类）
  async getCategoryTree(userId: string, type?: 'income' | 'expense'): Promise<(Category & { children: Category[] })[]> {
    const categories = await this.getCategories(userId, { type });
    
    const categoryMap = new Map<string, Category & { children: Category[] }>();
    const rootCategories: (Category & { children: Category[] })[] = [];

    // 初始化所有分类
    categories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // 构建树结构
    categories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id)!;
      
      if (category.parent_id) {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children.push(categoryWithChildren);
        } else {
          rootCategories.push(categoryWithChildren);
        }
      } else {
        rootCategories.push(categoryWithChildren);
      }
    });

    return rootCategories;
  }

  // 验证分类是否属于用户
  async verifyCategoryOwnership(categoryId: string, userId: string): Promise<boolean> {
    const { data, error } = await this.supabase
      .from('categories')
      .select('id')
      .eq('id', categoryId)
      .eq('user_id', userId)
      .single();

    if (error) {
      return false;
    }

    return !!data;
  }

  // 获取分类使用统计
  async getCategoryUsageStats(userId: string, options?: {
    startDate?: string;
    endDate?: string;
    type?: 'income' | 'expense';
  }) {
    let query = this.supabase
      .from('transactions')
      .select(`
        category_id,
        amount,
        category:categories(name, type, color)
      `)
      .eq('user_id', userId)
      .not('category_id', 'is', null);

    if (options?.startDate) {
      query = query.gte('date', options.startDate);
    }

    if (options?.endDate) {
      query = query.lte('date', options.endDate);
    }

    if (options?.type) {
      query = query.eq('type', options.type);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch category usage stats: ${error.message}`);
    }

    const stats = new Map<string, {
      id: string;
      name: string;
      type: string;
      color: string;
      totalAmount: number;
      transactionCount: number;
    }>();

    data?.forEach(transaction => {
      if (transaction.category) {
        const categoryId = transaction.category_id!;
        const existing = stats.get(categoryId);
        
        if (existing) {
          existing.totalAmount += transaction.amount;
          existing.transactionCount += 1;
        } else {
          stats.set(categoryId, {
            id: categoryId,
            name: transaction.category.name,
            type: transaction.category.type,
            color: transaction.category.color,
            totalAmount: transaction.amount,
            transactionCount: 1
          });
        }
      }
    });

    return Array.from(stats.values()).sort((a, b) => b.totalAmount - a.totalAmount);
  }

  // 获取预设分类图标选项
  getIconOptions() {
    return [
      'briefcase', 'award', 'trending-up', 'users', 'plus-circle',
      'utensils', 'car', 'shopping-bag', 'gamepad-2', 'heart',
      'book', 'home', 'zap', 'phone', 'shirt',
      'sparkles', 'gift', 'more-horizontal', 'circle', 'square',
      'triangle', 'star', 'heart', 'diamond', 'hexagon'
    ];
  }

  // 获取预设颜色选项
  getColorOptions() {
    return [
      '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',
      '#22C55E', '#10B981', '#06B6D4', '#3B82F6', '#6366F1',
      '#8B5CF6', '#A855F7', '#EC4899', '#F43F5E', '#6B7280',
      '#374151', '#1F2937', '#111827', '#059669', '#047857'
    ];
  }
}
