-- 记账软件数据库初始化脚本
-- 适用于 Supabase PostgreSQL 数据库
-- 执行顺序：1. 创建表结构 2. 设置RLS策略 3. 插入默认数据

-- ============================================================================
-- 1. 创建表结构
-- ============================================================================

-- 1.1 用户配置表 (user_settings) - 优先创建，其他表可能引用
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    default_currency TEXT DEFAULT 'CNY' NOT NULL,
    date_format TEXT DEFAULT 'YYYY-MM-DD' NOT NULL,
    language TEXT DEFAULT 'zh' NOT NULL CHECK (language IN ('zh', 'en')),
    theme TEXT DEFAULT 'system' NOT NULL CHECK (theme IN ('light', 'dark', 'system')),
    timezone TEXT DEFAULT 'Asia/Shanghai' NOT NULL,
    first_day_of_week INTEGER DEFAULT 1 NOT NULL CHECK (first_day_of_week BETWEEN 0 AND 6),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 1.2 账户表 (accounts)
CREATE TABLE IF NOT EXISTS accounts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('cash', 'bank_account', 'credit_card', 'investment', 'loan', 'other')),
    balance DECIMAL(15,2) DEFAULT 0.00 NOT NULL,
    currency TEXT DEFAULT 'CNY' NOT NULL,
    bank_name TEXT,
    account_number TEXT,
    credit_limit DECIMAL(15,2),
    interest_rate DECIMAL(5,4),
    is_active BOOLEAN DEFAULT true NOT NULL,
    is_excluded_from_total BOOLEAN DEFAULT false NOT NULL,
    sort_order INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 1.3 分类表 (categories)
CREATE TABLE IF NOT EXISTS categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
    color TEXT DEFAULT '#6B7280',
    icon TEXT DEFAULT 'circle',
    parent_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    is_system BOOLEAN DEFAULT false NOT NULL,
    sort_order INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 1.4 交易记录表 (transactions)
CREATE TABLE IF NOT EXISTS transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'transfer')),
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    description TEXT,
    date DATE NOT NULL,
    tags TEXT[] DEFAULT '{}',
    location TEXT,
    receipt_url TEXT,
    from_account_id UUID REFERENCES accounts(id) ON DELETE SET NULL,
    to_account_id UUID REFERENCES accounts(id) ON DELETE SET NULL,
    is_recurring BOOLEAN DEFAULT false NOT NULL,
    recurring_rule JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 转账时必须有来源和目标账户
    CONSTRAINT transfer_accounts_check 
        CHECK (
            (type = 'transfer' AND from_account_id IS NOT NULL AND to_account_id IS NOT NULL) 
            OR (type != 'transfer' AND from_account_id IS NULL AND to_account_id IS NULL)
        )
);

-- 1.5 预算表 (budgets)
CREATE TABLE IF NOT EXISTS budgets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    period TEXT NOT NULL CHECK (period IN ('weekly', 'monthly', 'quarterly', 'yearly', 'custom')),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    alert_threshold DECIMAL(5,2) DEFAULT 0.80 CHECK (alert_threshold BETWEEN 0 AND 1),
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    CONSTRAINT budget_date_check CHECK (end_date > start_date)
);

-- 1.6 定期交易模板表 (recurring_transactions)
CREATE TABLE IF NOT EXISTS recurring_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    account_id UUID REFERENCES accounts(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    description TEXT NOT NULL,
    frequency TEXT NOT NULL CHECK (frequency IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    start_date DATE NOT NULL,
    end_date DATE,
    next_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 1.7 财务目标表 (financial_goals)
CREATE TABLE IF NOT EXISTS financial_goals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    target_amount DECIMAL(15,2) NOT NULL CHECK (target_amount > 0),
    current_amount DECIMAL(15,2) DEFAULT 0.00 NOT NULL,
    target_date DATE,
    category TEXT NOT NULL CHECK (category IN ('saving', 'debt_payoff', 'investment', 'purchase', 'other')),
    priority INTEGER DEFAULT 1 CHECK (priority BETWEEN 1 AND 5),
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 1.8 债务表 (debts)
CREATE TABLE IF NOT EXISTS debts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    creditor_name TEXT NOT NULL,
    debt_type TEXT NOT NULL CHECK (debt_type IN ('credit_card', 'loan', 'mortgage', 'personal', 'other')),
    original_amount DECIMAL(15,2) NOT NULL CHECK (original_amount > 0),
    current_balance DECIMAL(15,2) NOT NULL CHECK (current_balance >= 0),
    interest_rate DECIMAL(5,4),
    minimum_payment DECIMAL(15,2),
    due_date INTEGER CHECK (due_date BETWEEN 1 AND 31),
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 1.9 投资组合表 (investments)
CREATE TABLE IF NOT EXISTS investments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    symbol TEXT,
    investment_type TEXT NOT NULL CHECK (investment_type IN ('stock', 'bond', 'fund', 'crypto', 'real_estate', 'other')),
    quantity DECIMAL(15,6) NOT NULL CHECK (quantity > 0),
    purchase_price DECIMAL(15,2) NOT NULL CHECK (purchase_price > 0),
    current_price DECIMAL(15,2),
    purchase_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- ============================================================================
-- 2. 创建索引
-- ============================================================================

-- 用户相关索引
CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(type);
CREATE INDEX IF NOT EXISTS idx_accounts_is_active ON accounts(is_active);

CREATE INDEX IF NOT EXISTS idx_categories_user_id ON categories(user_id);
CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);

CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_account_id ON transactions(account_id);
CREATE INDEX IF NOT EXISTS idx_transactions_category_id ON transactions(category_id);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);

CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON budgets(user_id);
CREATE INDEX IF NOT EXISTS idx_budgets_category_id ON budgets(category_id);
CREATE INDEX IF NOT EXISTS idx_budgets_period ON budgets(period);

CREATE INDEX IF NOT EXISTS idx_recurring_transactions_user_id ON recurring_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_recurring_transactions_next_date ON recurring_transactions(next_date);

CREATE INDEX IF NOT EXISTS idx_financial_goals_user_id ON financial_goals(user_id);
CREATE INDEX IF NOT EXISTS idx_debts_user_id ON debts(user_id);
CREATE INDEX IF NOT EXISTS idx_investments_user_id ON investments(user_id);

-- ============================================================================
-- 3. 创建触发器函数
-- ============================================================================

-- 更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器
DO $$
DECLARE
    table_name TEXT;
    tables TEXT[] := ARRAY['accounts', 'categories', 'transactions', 'budgets', 'user_settings', 'recurring_transactions', 'financial_goals', 'debts', 'investments'];
BEGIN
    FOREACH table_name IN ARRAY tables
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS update_%I_updated_at ON %I', table_name, table_name);
        EXECUTE format('CREATE TRIGGER update_%I_updated_at BEFORE UPDATE ON %I FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', table_name, table_name);
    END LOOP;
END
$$;

-- ============================================================================
-- 4. 启用行级安全策略 (RLS)
-- ============================================================================

-- 启用RLS
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE recurring_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE investments ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略的通用函数
CREATE OR REPLACE FUNCTION create_rls_policies(table_name TEXT)
RETURNS VOID AS $$
BEGIN
    -- 删除现有策略（如果存在）
    EXECUTE format('DROP POLICY IF EXISTS "Users can view their own %I" ON %I', table_name, table_name);
    EXECUTE format('DROP POLICY IF EXISTS "Users can insert their own %I" ON %I', table_name, table_name);
    EXECUTE format('DROP POLICY IF EXISTS "Users can update their own %I" ON %I', table_name, table_name);
    EXECUTE format('DROP POLICY IF EXISTS "Users can delete their own %I" ON %I', table_name, table_name);

    -- 创建新策略
    EXECUTE format('CREATE POLICY "Users can view their own %I" ON %I FOR SELECT USING (auth.uid() = user_id)', table_name, table_name);
    EXECUTE format('CREATE POLICY "Users can insert their own %I" ON %I FOR INSERT WITH CHECK (auth.uid() = user_id)', table_name, table_name);
    EXECUTE format('CREATE POLICY "Users can update their own %I" ON %I FOR UPDATE USING (auth.uid() = user_id)', table_name, table_name);
    EXECUTE format('CREATE POLICY "Users can delete their own %I" ON %I FOR DELETE USING (auth.uid() = user_id)', table_name, table_name);
END;
$$ LANGUAGE plpgsql;

-- 为所有表创建RLS策略
DO $$
DECLARE
    table_name TEXT;
    tables TEXT[] := ARRAY['accounts', 'categories', 'transactions', 'budgets', 'user_settings', 'recurring_transactions', 'financial_goals', 'debts', 'investments'];
BEGIN
    FOREACH table_name IN ARRAY tables
    LOOP
        PERFORM create_rls_policies(table_name);
    END LOOP;
END
$$;

-- 删除临时函数
DROP FUNCTION create_rls_policies(TEXT);

-- ============================================================================
-- 5. 业务逻辑函数
-- ============================================================================

-- 账户余额更新函数
CREATE OR REPLACE FUNCTION update_account_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- 如果是新增交易
    IF TG_OP = 'INSERT' THEN
        IF NEW.type = 'income' THEN
            -- 收入：增加账户余额
            UPDATE accounts
            SET balance = balance + NEW.amount
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'expense' THEN
            -- 支出：减少账户余额
            UPDATE accounts
            SET balance = balance - NEW.amount
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'transfer' THEN
            -- 转账：从来源账户减少，向目标账户增加
            UPDATE accounts
            SET balance = balance - NEW.amount
            WHERE id = NEW.from_account_id;

            UPDATE accounts
            SET balance = balance + NEW.amount
            WHERE id = NEW.to_account_id;
        END IF;
        RETURN NEW;
    END IF;

    -- 如果是更新交易
    IF TG_OP = 'UPDATE' THEN
        -- 先撤销旧交易的影响
        IF OLD.type = 'income' THEN
            UPDATE accounts
            SET balance = balance - OLD.amount
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'expense' THEN
            UPDATE accounts
            SET balance = balance + OLD.amount
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'transfer' THEN
            UPDATE accounts
            SET balance = balance + OLD.amount
            WHERE id = OLD.from_account_id;

            UPDATE accounts
            SET balance = balance - OLD.amount
            WHERE id = OLD.to_account_id;
        END IF;

        -- 再应用新交易的影响
        IF NEW.type = 'income' THEN
            UPDATE accounts
            SET balance = balance + NEW.amount
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'expense' THEN
            UPDATE accounts
            SET balance = balance - NEW.amount
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'transfer' THEN
            UPDATE accounts
            SET balance = balance - NEW.amount
            WHERE id = NEW.from_account_id;

            UPDATE accounts
            SET balance = balance + NEW.amount
            WHERE id = NEW.to_account_id;
        END IF;
        RETURN NEW;
    END IF;

    -- 如果是删除交易
    IF TG_OP = 'DELETE' THEN
        IF OLD.type = 'income' THEN
            UPDATE accounts
            SET balance = balance - OLD.amount
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'expense' THEN
            UPDATE accounts
            SET balance = balance + OLD.amount
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'transfer' THEN
            UPDATE accounts
            SET balance = balance + OLD.amount
            WHERE id = OLD.from_account_id;

            UPDATE accounts
            SET balance = balance - OLD.amount
            WHERE id = OLD.to_account_id;
        END IF;
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器：交易变更时自动更新账户余额
DROP TRIGGER IF EXISTS update_account_balance_trigger ON transactions;
CREATE TRIGGER update_account_balance_trigger
    AFTER INSERT OR UPDATE OR DELETE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_account_balance();

-- 计算下次定期交易日期的函数
CREATE OR REPLACE FUNCTION calculate_next_recurring_date(
    current_date DATE,
    frequency TEXT
) RETURNS DATE AS $$
BEGIN
    CASE frequency
        WHEN 'daily' THEN
            RETURN current_date + INTERVAL '1 day';
        WHEN 'weekly' THEN
            RETURN current_date + INTERVAL '1 week';
        WHEN 'monthly' THEN
            RETURN current_date + INTERVAL '1 month';
        WHEN 'quarterly' THEN
            RETURN current_date + INTERVAL '3 months';
        WHEN 'yearly' THEN
            RETURN current_date + INTERVAL '1 year';
        ELSE
            RETURN current_date + INTERVAL '1 month';
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 6. 默认数据创建函数
-- ============================================================================

-- 为新用户创建默认数据的函数
CREATE OR REPLACE FUNCTION create_default_user_data()
RETURNS TRIGGER AS $$
BEGIN
    -- 创建默认用户设置
    INSERT INTO user_settings (user_id, default_currency, language, theme) VALUES
        (NEW.id, 'CNY', 'zh', 'system');

    -- 创建默认收入分类
    INSERT INTO categories (user_id, name, type, color, icon, is_system, sort_order) VALUES
        (NEW.id, '工资', 'income', '#10B981', 'briefcase', true, 1),
        (NEW.id, '奖金', 'income', '#059669', 'award', true, 2),
        (NEW.id, '投资收益', 'income', '#047857', 'trending-up', true, 3),
        (NEW.id, '兼职', 'income', '#065F46', 'users', true, 4),
        (NEW.id, '其他收入', 'income', '#064E3B', 'plus-circle', true, 5);

    -- 创建默认支出分类
    INSERT INTO categories (user_id, name, type, color, icon, is_system, sort_order) VALUES
        (NEW.id, '餐饮', 'expense', '#EF4444', 'utensils', true, 1),
        (NEW.id, '交通', 'expense', '#F97316', 'car', true, 2),
        (NEW.id, '购物', 'expense', '#F59E0B', 'shopping-bag', true, 3),
        (NEW.id, '娱乐', 'expense', '#EAB308', 'gamepad-2', true, 4),
        (NEW.id, '医疗', 'expense', '#84CC16', 'heart', true, 5),
        (NEW.id, '教育', 'expense', '#22C55E', 'book', true, 6),
        (NEW.id, '房租', 'expense', '#06B6D4', 'home', true, 7),
        (NEW.id, '水电费', 'expense', '#3B82F6', 'zap', true, 8),
        (NEW.id, '通讯费', 'expense', '#6366F1', 'phone', true, 9),
        (NEW.id, '服装', 'expense', '#8B5CF6', 'shirt', true, 10),
        (NEW.id, '美容', 'expense', '#A855F7', 'sparkles', true, 11),
        (NEW.id, '礼品', 'expense', '#EC4899', 'gift', true, 12),
        (NEW.id, '其他支出', 'expense', '#6B7280', 'more-horizontal', true, 13);

    -- 创建默认账户
    INSERT INTO accounts (user_id, name, type, balance, sort_order) VALUES
        (NEW.id, '现金', 'cash', 0.00, 1),
        (NEW.id, '银行卡', 'bank_account', 0.00, 2),
        (NEW.id, '信用卡', 'credit_card', 0.00, 3),
        (NEW.id, '支付宝', 'other', 0.00, 4),
        (NEW.id, '微信支付', 'other', 0.00, 5);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器：用户注册时自动创建默认数据
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION create_default_user_data();

-- ============================================================================
-- 7. 实用函数
-- ============================================================================

-- 获取用户总资产函数
CREATE OR REPLACE FUNCTION get_user_total_assets(user_uuid UUID)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    total_assets DECIMAL(15,2) := 0;
BEGIN
    SELECT COALESCE(SUM(balance), 0) INTO total_assets
    FROM accounts
    WHERE user_id = user_uuid
    AND is_active = true
    AND is_excluded_from_total = false;

    RETURN total_assets;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 获取用户月度支出统计函数
CREATE OR REPLACE FUNCTION get_monthly_expense_stats(
    user_uuid UUID,
    target_year INTEGER,
    target_month INTEGER
)
RETURNS TABLE(
    category_name TEXT,
    category_color TEXT,
    total_amount DECIMAL(15,2),
    transaction_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.name as category_name,
        c.color as category_color,
        COALESCE(SUM(t.amount), 0) as total_amount,
        COUNT(t.id)::INTEGER as transaction_count
    FROM categories c
    LEFT JOIN transactions t ON c.id = t.category_id
        AND t.type = 'expense'
        AND EXTRACT(YEAR FROM t.date) = target_year
        AND EXTRACT(MONTH FROM t.date) = target_month
        AND t.user_id = user_uuid
    WHERE c.user_id = user_uuid
    AND c.type = 'expense'
    AND c.is_active = true
    GROUP BY c.id, c.name, c.color, c.sort_order
    ORDER BY c.sort_order, total_amount DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 8. 完成初始化
-- ============================================================================

-- 显示初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE '记账软件数据库初始化完成！';
    RAISE NOTICE '=================================================';
    RAISE NOTICE '已创建的表：';
    RAISE NOTICE '- user_settings (用户设置)';
    RAISE NOTICE '- accounts (账户)';
    RAISE NOTICE '- categories (分类)';
    RAISE NOTICE '- transactions (交易记录)';
    RAISE NOTICE '- budgets (预算)';
    RAISE NOTICE '- recurring_transactions (定期交易)';
    RAISE NOTICE '- financial_goals (财务目标)';
    RAISE NOTICE '- debts (债务)';
    RAISE NOTICE '- investments (投资)';
    RAISE NOTICE '';
    RAISE NOTICE '已启用功能：';
    RAISE NOTICE '- 行级安全策略 (RLS)';
    RAISE NOTICE '- 自动更新时间戳';
    RAISE NOTICE '- 账户余额自动计算';
    RAISE NOTICE '- 新用户默认数据创建';
    RAISE NOTICE '';
    RAISE NOTICE '使用说明：';
    RAISE NOTICE '1. 用户注册后会自动创建默认分类和账户';
    RAISE NOTICE '2. 所有交易会自动更新相关账户余额';
    RAISE NOTICE '3. 支持收入、支出、转账三种交易类型';
    RAISE NOTICE '4. 支持预算管理和财务目标跟踪';
    RAISE NOTICE '5. 支持定期交易和投资组合管理';
    RAISE NOTICE '=================================================';
END;
$$;

/*
使用方法：
1. 在 Supabase SQL Editor 中执行此脚本
2. 确保已启用 Row Level Security
3. 用户注册后会自动创建默认数据
4. 可以通过 Supabase 客户端库进行数据操作

示例查询：
-- 获取用户总资产
SELECT get_user_total_assets(auth.uid());

-- 获取当月支出统计
SELECT * FROM get_monthly_expense_stats(auth.uid(), 2025, 1);

-- 查看用户账户
SELECT * FROM accounts WHERE user_id = auth.uid();

-- 查看用户交易记录
SELECT * FROM transactions WHERE user_id = auth.uid() ORDER BY date DESC;
*/

-- ============================================================================
-- 4. 启用行级安全策略 (RLS)
-- ============================================================================

-- 启用RLS
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE recurring_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE investments ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略的通用函数
CREATE OR REPLACE FUNCTION create_rls_policies(table_name TEXT)
RETURNS VOID AS $$
BEGIN
    -- 删除现有策略（如果存在）
    EXECUTE format('DROP POLICY IF EXISTS "Users can view their own %I" ON %I', table_name, table_name);
    EXECUTE format('DROP POLICY IF EXISTS "Users can insert their own %I" ON %I', table_name, table_name);
    EXECUTE format('DROP POLICY IF EXISTS "Users can update their own %I" ON %I', table_name, table_name);
    EXECUTE format('DROP POLICY IF EXISTS "Users can delete their own %I" ON %I', table_name, table_name);

    -- 创建新策略
    EXECUTE format('CREATE POLICY "Users can view their own %I" ON %I FOR SELECT USING (auth.uid() = user_id)', table_name, table_name);
    EXECUTE format('CREATE POLICY "Users can insert their own %I" ON %I FOR INSERT WITH CHECK (auth.uid() = user_id)', table_name, table_name);
    EXECUTE format('CREATE POLICY "Users can update their own %I" ON %I FOR UPDATE USING (auth.uid() = user_id)', table_name, table_name);
    EXECUTE format('CREATE POLICY "Users can delete their own %I" ON %I FOR DELETE USING (auth.uid() = user_id)', table_name, table_name);
END;
$$ LANGUAGE plpgsql;

-- 为所有表创建RLS策略
DO $$
DECLARE
    table_name TEXT;
    tables TEXT[] := ARRAY['accounts', 'categories', 'transactions', 'budgets', 'user_settings', 'recurring_transactions', 'financial_goals', 'debts', 'investments'];
BEGIN
    FOREACH table_name IN ARRAY tables
    LOOP
        PERFORM create_rls_policies(table_name);
    END LOOP;
END
$$;

-- 删除临时函数
DROP FUNCTION create_rls_policies(TEXT);
