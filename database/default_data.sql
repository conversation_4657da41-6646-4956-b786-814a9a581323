-- 默认分类数据
-- 这些是系统预设的分类，用户注册后会自动创建

-- 创建函数：为新用户创建默认数据
CREATE OR REPLACE FUNCTION create_default_user_data()
RETURNS TRIGGER AS $$
BEGIN
    -- 创建默认收入分类
    INSERT INTO categories (user_id, name, type, color, icon) VALUES
        (NEW.id, '工资', 'income', '#10B981', 'briefcase'),
        (NEW.id, '奖金', 'income', '#059669', 'award'),
        (NEW.id, '投资收益', 'income', '#047857', 'trending-up'),
        (NEW.id, '兼职', 'income', '#065F46', 'users'),
        (NEW.id, '其他收入', 'income', '#064E3B', 'plus-circle');

    -- 创建默认支出分类
    INSERT INTO categories (user_id, name, type, color, icon) VALUES
        (NEW.id, '餐饮', 'expense', '#EF4444', 'utensils'),
        (NEW.id, '交通', 'expense', '#F97316', 'car'),
        (NEW.id, '购物', 'expense', '#F59E0B', 'shopping-bag'),
        (NEW.id, '娱乐', 'expense', '#EAB308', 'gamepad-2'),
        (NEW.id, '医疗', 'expense', '#84CC16', 'heart'),
        (NEW.id, '教育', 'expense', '#22C55E', 'book'),
        (NEW.id, '房租', 'expense', '#06B6D4', 'home'),
        (NEW.id, '水电费', 'expense', '#3B82F6', 'zap'),
        (NEW.id, '通讯费', 'expense', '#6366F1', 'phone'),
        (NEW.id, '服装', 'expense', '#8B5CF6', 'shirt'),
        (NEW.id, '美容', 'expense', '#A855F7', 'sparkles'),
        (NEW.id, '礼品', 'expense', '#EC4899', 'gift'),
        (NEW.id, '其他支出', 'expense', '#6B7280', 'more-horizontal');

    -- 创建默认账户
    INSERT INTO accounts (user_id, name, type, balance) VALUES
        (NEW.id, '现金', 'cash', 0.00),
        (NEW.id, '银行卡', 'bank_account', 0.00),
        (NEW.id, '信用卡', 'credit_card', 0.00),
        (NEW.id, '支付宝', 'other', 0.00),
        (NEW.id, '微信支付', 'other', 0.00);

    -- 创建默认用户设置
    INSERT INTO user_settings (user_id, default_currency, language, theme) VALUES
        (NEW.id, 'CNY', 'zh', 'system');

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器：用户注册时自动创建默认数据
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION create_default_user_data();

-- 创建函数：更新账户余额
CREATE OR REPLACE FUNCTION update_account_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- 如果是新增交易
    IF TG_OP = 'INSERT' THEN
        IF NEW.type = 'income' THEN
            -- 收入：增加账户余额
            UPDATE accounts 
            SET balance = balance + NEW.amount 
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'expense' THEN
            -- 支出：减少账户余额
            UPDATE accounts 
            SET balance = balance - NEW.amount 
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'transfer' THEN
            -- 转账：从来源账户减少，向目标账户增加
            UPDATE accounts 
            SET balance = balance - NEW.amount 
            WHERE id = NEW.from_account_id;
            
            UPDATE accounts 
            SET balance = balance + NEW.amount 
            WHERE id = NEW.to_account_id;
        END IF;
        RETURN NEW;
    END IF;

    -- 如果是更新交易
    IF TG_OP = 'UPDATE' THEN
        -- 先撤销旧交易的影响
        IF OLD.type = 'income' THEN
            UPDATE accounts 
            SET balance = balance - OLD.amount 
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'expense' THEN
            UPDATE accounts 
            SET balance = balance + OLD.amount 
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'transfer' THEN
            UPDATE accounts 
            SET balance = balance + OLD.amount 
            WHERE id = OLD.from_account_id;
            
            UPDATE accounts 
            SET balance = balance - OLD.amount 
            WHERE id = OLD.to_account_id;
        END IF;

        -- 再应用新交易的影响
        IF NEW.type = 'income' THEN
            UPDATE accounts 
            SET balance = balance + NEW.amount 
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'expense' THEN
            UPDATE accounts 
            SET balance = balance - NEW.amount 
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'transfer' THEN
            UPDATE accounts 
            SET balance = balance - NEW.amount 
            WHERE id = NEW.from_account_id;
            
            UPDATE accounts 
            SET balance = balance + NEW.amount 
            WHERE id = NEW.to_account_id;
        END IF;
        RETURN NEW;
    END IF;

    -- 如果是删除交易
    IF TG_OP = 'DELETE' THEN
        IF OLD.type = 'income' THEN
            UPDATE accounts 
            SET balance = balance - OLD.amount 
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'expense' THEN
            UPDATE accounts 
            SET balance = balance + OLD.amount 
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'transfer' THEN
            UPDATE accounts 
            SET balance = balance + OLD.amount 
            WHERE id = OLD.from_account_id;
            
            UPDATE accounts 
            SET balance = balance - OLD.amount 
            WHERE id = OLD.to_account_id;
        END IF;
        RETURN OLD;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器：交易变更时自动更新账户余额
CREATE TRIGGER update_account_balance_trigger
    AFTER INSERT OR UPDATE OR DELETE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_account_balance();
