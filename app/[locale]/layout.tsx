import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { Toaster } from "@/components/ui/sonner";
import { AppHeader } from "@/components/app-header";
import { AppFooter } from "@/components/app-footer";
import "../globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  display: "swap",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "生财记 - 智能记账与财富管理工具 | WealthNote - Smart Expense Tracker & Personal Finance Manager",
  description: "生财记（WealthNote）是一款智能记账与财富管理工具，帮您轻松记录每一笔收支，实时统计资产、收入、支出与结余。支持多端同步、分类管理与财务报表，让理财更简单、更高效，助您实现财富增长。WealthNote is a smart expense tracker and personal finance manager that helps you easily record every transaction, track income and expenses, and monitor your assets in real time. Enjoy multi-device sync, category management, and detailed financial reports to make budgeting simple and boost your wealth.",
};

export default async function LocaleLayout({
  children,
  params
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  const messages = await getMessages();

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={`${geistSans.className} antialiased`}>
        <NextIntlClientProvider messages={messages}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <div className="min-h-screen flex flex-col">
              <AppHeader />
              <main className="flex-1">
                {children}
              </main>
              <AppFooter />
            </div>
            <Toaster />
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
