@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations for the professional homepage */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }

  33% {
    transform: translate(30px, -50px) scale(1.1);
  }

  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }

  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Grid pattern background */
.bg-grid-white\/\[0\.02\] {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
}

/* Price comparison animations */
@keyframes slash {
  0% {
    transform: scale(0) rotate(-45deg);
    opacity: 0;
  }

  25% {
    transform: scale(1.3) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 20px rgba(255, 193, 7, 0.8));
  }

  50% {
    transform: scale(1.1) rotate(15deg);
    opacity: 0.9;
  }

  75% {
    transform: scale(1.2) rotate(-10deg);
    opacity: 0.8;
  }

  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.7;
  }
}

@keyframes price-drop {
  0% {
    transform: translateY(-30px) scale(0.5) rotate(-10deg);
    opacity: 0;
    filter: blur(2px);
  }

  30% {
    transform: translateY(-15px) scale(0.8) rotate(5deg);
    opacity: 0.5;
    filter: blur(1px);
  }

  60% {
    transform: translateY(-5px) scale(1.1) rotate(-2deg);
    opacity: 0.8;
    filter: blur(0px);
  }

  80% {
    transform: translateY(2px) scale(1.05) rotate(1deg);
    opacity: 0.9;
  }

  100% {
    transform: translateY(0) scale(1) rotate(0deg);
    opacity: 1;
    filter: drop-shadow(0 0 15px rgba(34, 197, 94, 0.6));
  }
}

@keyframes savings-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

.animate-slash {
  animation: slash 3s ease-in-out infinite;
}

.animate-price-drop {
  animation: price-drop 2s ease-out infinite;
  animation-delay: 1s;
}

.animate-savings-pulse {
  animation: savings-pulse 2s ease-in-out infinite;
  animation-delay: 2s;
}