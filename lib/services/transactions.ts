import { createClient } from "@/lib/supabase/client";
import { 
  Transaction, 
  InsertTransaction, 
  UpdateTransaction, 
  TransactionWithDetails 
} from "@/lib/types/database";

export class TransactionService {
  private supabase = createClient();

  // 获取用户的所有交易记录
  async getTransactions(userId: string, options?: {
    limit?: number;
    offset?: number;
    type?: 'income' | 'expense' | 'transfer';
    accountId?: string;
    categoryId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<TransactionWithDetails[]> {
    let query = this.supabase
      .from('transactions')
      .select(`
        *,
        account:accounts(*),
        category:categories(*),
        from_account:accounts!transactions_from_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .eq('user_id', userId)
      .order('date', { ascending: false })
      .order('created_at', { ascending: false });

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
    }

    if (options?.type) {
      query = query.eq('type', options.type);
    }

    if (options?.accountId) {
      query = query.eq('account_id', options.accountId);
    }

    if (options?.categoryId) {
      query = query.eq('category_id', options.categoryId);
    }

    if (options?.startDate) {
      query = query.gte('date', options.startDate);
    }

    if (options?.endDate) {
      query = query.lte('date', options.endDate);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch transactions: ${error.message}`);
    }

    return data || [];
  }

  // 获取单个交易记录
  async getTransaction(id: string): Promise<TransactionWithDetails | null> {
    const { data, error } = await this.supabase
      .from('transactions')
      .select(`
        *,
        account:accounts(*),
        category:categories(*),
        from_account:accounts!transactions_from_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw new Error(`Failed to fetch transaction: ${error.message}`);
    }

    return data;
  }

  // 创建新交易记录
  async createTransaction(transaction: InsertTransaction): Promise<Transaction> {
    const { data, error } = await this.supabase
      .from('transactions')
      .insert(transaction)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create transaction: ${error.message}`);
    }

    return data;
  }

  // 更新交易记录
  async updateTransaction(id: string, updates: UpdateTransaction): Promise<Transaction> {
    const { data, error } = await this.supabase
      .from('transactions')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update transaction: ${error.message}`);
    }

    return data;
  }

  // 删除交易记录
  async deleteTransaction(id: string): Promise<void> {
    const { error } = await this.supabase
      .from('transactions')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete transaction: ${error.message}`);
    }
  }

  // 获取交易统计数据
  async getTransactionStats(userId: string, options?: {
    startDate?: string;
    endDate?: string;
  }) {
    let query = this.supabase
      .from('transactions')
      .select('type, amount')
      .eq('user_id', userId);

    if (options?.startDate) {
      query = query.gte('date', options.startDate);
    }

    if (options?.endDate) {
      query = query.lte('date', options.endDate);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch transaction stats: ${error.message}`);
    }

    const stats = {
      totalIncome: 0,
      totalExpense: 0,
      totalTransfer: 0,
      netIncome: 0,
      transactionCount: data?.length || 0
    };

    data?.forEach(transaction => {
      switch (transaction.type) {
        case 'income':
          stats.totalIncome += transaction.amount;
          break;
        case 'expense':
          stats.totalExpense += transaction.amount;
          break;
        case 'transfer':
          stats.totalTransfer += transaction.amount;
          break;
      }
    });

    stats.netIncome = stats.totalIncome - stats.totalExpense;

    return stats;
  }

  // 按分类获取支出统计
  async getExpenseByCategory(userId: string, options?: {
    startDate?: string;
    endDate?: string;
  }) {
    let query = this.supabase
      .from('transactions')
      .select(`
        amount,
        category:categories(name, color)
      `)
      .eq('user_id', userId)
      .eq('type', 'expense');

    if (options?.startDate) {
      query = query.gte('date', options.startDate);
    }

    if (options?.endDate) {
      query = query.lte('date', options.endDate);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch expense by category: ${error.message}`);
    }

    const categoryStats = new Map<string, { name: string; amount: number; color: string }>();

    data?.forEach(transaction => {
      if (transaction.category) {
        const categoryName = transaction.category.name;
        const existing = categoryStats.get(categoryName);
        
        if (existing) {
          existing.amount += transaction.amount;
        } else {
          categoryStats.set(categoryName, {
            name: categoryName,
            amount: transaction.amount,
            color: transaction.category.color
          });
        }
      }
    });

    return Array.from(categoryStats.values()).sort((a, b) => b.amount - a.amount);
  }

  // 搜索交易记录
  async searchTransactions(userId: string, searchTerm: string, options?: {
    limit?: number;
    type?: 'income' | 'expense' | 'transfer';
  }): Promise<TransactionWithDetails[]> {
    let query = this.supabase
      .from('transactions')
      .select(`
        *,
        account:accounts(*),
        category:categories(*),
        from_account:accounts!transactions_from_account_id_fkey(*),
        to_account:accounts!transactions_to_account_id_fkey(*)
      `)
      .eq('user_id', userId)
      .or(`description.ilike.%${searchTerm}%,tags.cs.{${searchTerm}}`)
      .order('date', { ascending: false });

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.type) {
      query = query.eq('type', options.type);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to search transactions: ${error.message}`);
    }

    return data || [];
  }
}
