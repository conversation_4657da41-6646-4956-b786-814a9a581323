import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Plus,
  Search,
  Filter,
  Download,
  TrendingUp,
  TrendingDown,
  ArrowRightLeft
} from "lucide-react";
import Link from "next/link";

export default function TransactionsPage() {
  const t = useTranslations();

  // Mock data - 这些数据稍后会从数据库获取
  const transactions = [
    {
      id: 1,
      type: 'expense',
      amount: 85.50,
      category: '餐饮',
      description: '午餐 - 麦当劳',
      date: '2025-01-09',
      time: '12:30',
      account: '招商银行',
      tags: ['工作日', '快餐']
    },
    {
      id: 2,
      type: 'income',
      amount: 5000.00,
      category: '工资',
      description: '月薪发放',
      date: '2025-01-08',
      time: '09:00',
      account: '工商银行',
      tags: ['固定收入']
    },
    {
      id: 3,
      type: 'expense',
      amount: 120.00,
      category: '交通',
      description: '地铁卡充值',
      date: '2025-01-07',
      time: '08:15',
      account: '支付宝',
      tags: ['通勤']
    },
    {
      id: 4,
      type: 'transfer',
      amount: 1000.00,
      category: '转账',
      description: '从储蓄账户转入',
      date: '2025-01-06',
      time: '14:20',
      account: '建设银行',
      tags: ['内部转账']
    },
    {
      id: 5,
      type: 'expense',
      amount: 299.00,
      category: '购物',
      description: '运动鞋',
      date: '2025-01-05',
      time: '16:45',
      account: '信用卡',
      tags: ['服装', '运动']
    }
  ];

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'income':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'expense':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      case 'transfer':
        return <ArrowRightLeft className="h-4 w-4 text-blue-600" />;
      default:
        return <TrendingDown className="h-4 w-4" />;
    }
  };

  const getAmountColor = (type: string) => {
    switch (type) {
      case 'income':
        return 'text-green-600';
      case 'expense':
        return 'text-red-600';
      case 'transfer':
        return 'text-blue-600';
      default:
        return 'text-foreground';
    }
  };

  const getAmountPrefix = (type: string) => {
    switch (type) {
      case 'income':
        return '+';
      case 'expense':
        return '-';
      case 'transfer':
        return '→';
      default:
        return '';
    }
  };

  return (
    <div className="container py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('transactions.title')}</h1>
          <p className="text-muted-foreground">
            管理您的所有交易记录
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            {t('common.export')}
          </Button>
          <Button asChild>
            <Link href="/transactions/add">
              <Plus className="mr-2 h-4 w-4" />
              {t('transactions.addTransaction')}
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">筛选和搜索</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 md:flex-row md:items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索交易记录..."
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                类型
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                分类
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                账户
              </Button>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                日期
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transactions List */}
      <Card>
        <CardHeader>
          <CardTitle>交易记录</CardTitle>
          <CardDescription>
            共 {transactions.length} 条记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors">
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0">
                    {getTransactionIcon(transaction.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="font-medium truncate">{transaction.description}</p>
                      <Badge variant="secondary" className="text-xs">
                        {transaction.category}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>{transaction.account}</span>
                      <span>{transaction.date} {transaction.time}</span>
                    </div>
                    {transaction.tags.length > 0 && (
                      <div className="flex gap-1 mt-2">
                        {transaction.tags.map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <div className="text-right flex-shrink-0">
                  <p className={`font-semibold text-lg ${getAmountColor(transaction.type)}`}>
                    {getAmountPrefix(transaction.type)}¥{transaction.amount.toFixed(2)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
