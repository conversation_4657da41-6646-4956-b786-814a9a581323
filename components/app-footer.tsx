"use client";

import { useTranslations } from 'next-intl';
import { usePathname } from 'next/navigation';

export function AppFooter() {
  const t = useTranslations();
  const pathname = usePathname();
  
  // Only show footer on home page
  const isHomePage = pathname === '/' || pathname === '/zh' || pathname === '/en';
  
  if (!isHomePage) {
    return null;
  }

  return (
    <footer className="w-full border-t bg-background">
      <div className="container flex flex-col items-center justify-center gap-4 py-8 text-center">
        <div className="flex flex-col gap-2">
          <p className="text-sm text-muted-foreground">
            © 2025 {t('app.title')}. All rights reserved.
          </p>
          <p className="text-xs text-muted-foreground">
            {t('app.description')}
          </p>
        </div>
        
        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          <a
            href="https://supabase.com"
            target="_blank"
            className="hover:text-foreground transition-colors"
            rel="noreferrer"
          >
            Powered by Supabase
          </a>
          <span>•</span>
          <a
            href="https://nextjs.org"
            target="_blank"
            className="hover:text-foreground transition-colors"
            rel="noreferrer"
          >
            Built with Next.js
          </a>
        </div>
      </div>
    </footer>
  );
}
