import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Plus,
  TrendingUp,
  TrendingDown,
  Wallet,
  CreditCard,
  Pie<PERSON>hart,
  BarChart3
} from "lucide-react";
import Link from "next/link";

export default function DashboardPage() {
  const t = useTranslations();

  // Mock data - 这些数据稍后会从数据库获取
  const stats = {
    totalBalance: 15680.50,
    monthlyIncome: 8500.00,
    monthlyExpense: 3200.00,
    savingsRate: 62.4
  };

  const recentTransactions = [
    {
      id: 1,
      type: 'expense',
      amount: 85.50,
      category: '餐饮',
      description: '午餐',
      date: '2025-01-09',
      account: '招商银行'
    },
    {
      id: 2,
      type: 'income',
      amount: 5000.00,
      category: '工资',
      description: '月薪',
      date: '2025-01-08',
      account: '工商银行'
    },
    {
      id: 3,
      type: 'expense',
      amount: 120.00,
      category: '交通',
      description: '地铁卡充值',
      date: '2025-01-07',
      account: '支付宝'
    }
  ];

  return (
    <div className="container py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('dashboard.title')}</h1>
          <p className="text-muted-foreground">
            欢迎回来，查看您的财务概况
          </p>
        </div>
        <Button asChild>
          <Link href="/transactions/add">
            <Plus className="mr-2 h-4 w-4" />
            {t('transactions.addTransaction')}
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.totalBalance')}
            </CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ¥{stats.totalBalance.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
            </div>
            <p className="text-xs text-muted-foreground">
              较上月 +12.5%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.monthlyIncome')}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ¥{stats.monthlyIncome.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
            </div>
            <p className="text-xs text-muted-foreground">
              较上月 +8.2%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('dashboard.monthlyExpense')}
            </CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              ¥{stats.monthlyExpense.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
            </div>
            <p className="text-xs text-muted-foreground">
              较上月 -5.1%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              储蓄率
            </CardTitle>
            <PieChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.savingsRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              健康水平
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              {t('dashboard.recentTransactions')}
              <Button variant="outline" size="sm" asChild>
                <Link href="/transactions">查看全部</Link>
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-2 h-2 rounded-full ${
                      transaction.type === 'income' ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-muted-foreground">
                        {transaction.category} • {transaction.account}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-medium ${
                      transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.type === 'income' ? '+' : '-'}¥{transaction.amount.toFixed(2)}
                    </p>
                    <p className="text-sm text-muted-foreground">{transaction.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>
              常用功能快速入口
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              <Button variant="outline" className="justify-start" asChild>
                <Link href="/transactions/add">
                  <Plus className="mr-2 h-4 w-4" />
                  添加交易记录
                </Link>
              </Button>
              <Button variant="outline" className="justify-start" asChild>
                <Link href="/accounts">
                  <CreditCard className="mr-2 h-4 w-4" />
                  管理账户
                </Link>
              </Button>
              <Button variant="outline" className="justify-start" asChild>
                <Link href="/categories">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  管理分类
                </Link>
              </Button>
              <Button variant="outline" className="justify-start" asChild>
                <Link href="/reports">
                  <PieChart className="mr-2 h-4 w-4" />
                  查看报表
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
